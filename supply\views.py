from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login, logout
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, F
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from .forms import UserRegistrationForm, UserProfileForm, SupplyItemForm, StockAdjustmentForm, SupplyRequestForm, RequestFilterForm, ItemSearchForm, BatchRequestForm, UserManagementForm, UserSearchForm, BatchRequestApprovalForm, SupplyCategoryForm, CategoryFilterForm, ReleaseForm, ReleaseFilterForm, ReleaseApprovalForm, ReleaseDeploymentForm
from .models import SupplyItem, InventoryTransaction, SupplyRequest, UserProfile, SupplyCategory, Release
from django.contrib.auth.models import User
from .decorators import role_required
from .audit import log_audit_event, log_request_action, log_bulk_operation

# Create your views here.

def register(request):
    """User registration view"""
    if request.user.is_authenticated:
        return redirect('supply:dashboard')
    
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'Registration successful! Welcome to MSRRMS.')
            return redirect('supply:dashboard')
    else:
        form = UserRegistrationForm()
    
    return render(request, 'registration/register.html', {'form': form})


@login_required
def profile(request):
    """User profile management view"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user.userprofile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('supply:profile')
    else:
        form = UserProfileForm(instance=request.user.userprofile, user=request.user)
    
    return render(request, 'registration/profile.html', {'form': form})


@login_required
def dashboard(request):
    """Main dashboard for all users"""
    if hasattr(request.user, 'userprofile') and request.user.userprofile.role == 'GSO':
        return redirect('supply:gso_dashboard')
    else:
        # Show department dashboard with recent requests
        user_requests = SupplyRequest.objects.filter(requester=request.user)
        recent_requests = user_requests.select_related('item').order_by('-created_at')[:5]

        # Calculate statistics
        pending_count = user_requests.filter(status='PENDING').count()
        approved_count = user_requests.filter(status='APPROVED').count()
        released_count = user_requests.filter(status='RELEASED').count()
        rejected_count = user_requests.filter(status='REJECTED').count()
        total_count = user_requests.count()

        context = {
            'recent_requests': recent_requests,
            'pending_count': pending_count,
            'approved_count': approved_count,
            'released_count': released_count,
            'rejected_count': rejected_count,
            'total_count': total_count,
        }
        return render(request, 'supply/dashboard.html', context)


def export_requests(request, format_type):
    """Export filtered requests to CSV or Excel"""
    import csv
    from django.http import HttpResponse
    from datetime import datetime
    from django.db.models import Q

    # Get the same filters as the main view
    status_filter = request.GET.get('status', '')
    department_filter = request.GET.get('department', '')
    category_filter = request.GET.get('category', '')
    search_query = request.GET.get('search', '').strip()
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    date_type = request.GET.get('date_type', 'created')

    # Build queryset with same filters
    requests = SupplyRequest.objects.select_related(
        'item', 'item__category', 'requester', 'requester__userprofile',
        'approved_by', 'released_by'
    )

    # Apply filters (same logic as main view)
    if status_filter:
        requests = requests.filter(status=status_filter)
    if department_filter:
        requests = requests.filter(requester__userprofile__department__icontains=department_filter)
    if category_filter:
        requests = requests.filter(item__category_id=category_filter)
    if search_query:
        requests = requests.filter(
            Q(request_id__icontains=search_query) |
            Q(item__name__icontains=search_query) |
            Q(requester__username__icontains=search_query) |
            Q(purpose__icontains=search_query)
        )

    # Apply date filters
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            if date_type == 'created':
                requests = requests.filter(created_at__date__gte=date_from_obj)
            elif date_type == 'approved':
                requests = requests.filter(approved_at__date__gte=date_from_obj)
            elif date_type == 'released':
                requests = requests.filter(released_at__date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            if date_type == 'created':
                requests = requests.filter(created_at__date__lte=date_to_obj)
            elif date_type == 'approved':
                requests = requests.filter(approved_at__date__lte=date_to_obj)
            elif date_type == 'released':
                requests = requests.filter(released_at__date__lte=date_to_obj)
        except ValueError:
            pass

    # Create response
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'supply_requests_{timestamp}.csv'

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'Request ID', 'Status', 'Item Name', 'Category', 'Quantity', 'Unit',
        'Requester', 'Department', 'Purpose', 'Created Date', 'Approved Date',
        'Released Date', 'Approved By', 'Released By', 'Stock Available'
    ])

    # Write data
    for req in requests.order_by('-created_at'):
        writer.writerow([
            req.request_id,
            req.get_status_display(),
            req.item.name,
            req.item.category.name if req.item.category else 'Uncategorized',
            req.quantity,
            req.item.unit,
            req.requester.get_full_name() or req.requester.username,
            req.requester.userprofile.department if hasattr(req.requester, 'userprofile') else '',
            req.purpose or '',
            req.created_at.strftime('%Y-%m-%d %H:%M'),
            req.approved_at.strftime('%Y-%m-%d %H:%M') if req.approved_at else '',
            req.released_at.strftime('%Y-%m-%d %H:%M') if req.released_at else '',
            req.approved_by.get_full_name() if req.approved_by else '',
            req.released_by.get_full_name() if req.released_by else '',
            req.item.current_stock
        ])

    return response


@login_required
@role_required('GSO')
def gso_requests_dashboard(request):
    """Comprehensive GSO dashboard for managing all supply requests"""
    from datetime import datetime, timedelta
    from django.utils import timezone
    from django.db.models import Q, Count

    # Check if this is an export request
    export_format = request.GET.get('export')
    if export_format in ['csv', 'excel']:
        return export_requests(request, export_format)

    # Get filter parameters with validation
    status_filter = request.GET.get('status', '')
    department_filter = request.GET.get('department', '')
    category_filter = request.GET.get('category', '')
    priority_filter = request.GET.get('priority', '')
    search_query = request.GET.get('search', '').strip()
    sort_by = request.GET.get('sort', '-created_at')

    # Date range filters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    date_type = request.GET.get('date_type', 'created')  # created, approved, released

    # Pagination
    page_size = int(request.GET.get('page_size', 20))
    page_size = min(page_size, 100)  # Limit max page size

    # Base queryset with optimized joins
    requests = SupplyRequest.objects.select_related(
        'item', 'item__category', 'requester', 'requester__userprofile',
        'approved_by', 'released_by'
    ).prefetch_related('request_items__item')

    # Apply status filter
    if status_filter:
        requests = requests.filter(status=status_filter)

    # Apply department filter
    if department_filter:
        requests = requests.filter(requester__userprofile__department__icontains=department_filter)

    # Apply category filter
    if category_filter:
        requests = requests.filter(item__category_id=category_filter)

    # Apply priority filter (if priority field exists)
    if priority_filter and hasattr(SupplyRequest, 'priority'):
        requests = requests.filter(priority=priority_filter)

    # Apply search filter
    if search_query:
        requests = requests.filter(
            Q(request_id__icontains=search_query) |
            Q(item__name__icontains=search_query) |
            Q(requester__username__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query) |
            Q(purpose__icontains=search_query)
        )

    # Apply date range filters
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            if date_type == 'created':
                requests = requests.filter(created_at__date__gte=date_from_obj)
            elif date_type == 'approved':
                requests = requests.filter(approved_at__date__gte=date_from_obj)
            elif date_type == 'released':
                requests = requests.filter(released_at__date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            if date_type == 'created':
                requests = requests.filter(created_at__date__lte=date_to_obj)
            elif date_type == 'approved':
                requests = requests.filter(approved_at__date__lte=date_to_obj)
            elif date_type == 'released':
                requests = requests.filter(released_at__date__lte=date_to_obj)
        except ValueError:
            pass

    # Apply sorting
    valid_sort_fields = [
        'created_at', '-created_at', 'approved_at', '-approved_at', 'released_at', '-released_at',
        'item__name', '-item__name', 'quantity', '-quantity', 'status', '-status',
        'requester__userprofile__department', '-requester__userprofile__department',
        'requester__username', '-requester__username'
    ]
    if sort_by in valid_sort_fields:
        requests = requests.order_by(sort_by)
    else:
        requests = requests.order_by('-created_at')

    # Calculate statistics
    total_requests = SupplyRequest.objects.count()
    pending_requests = SupplyRequest.objects.filter(status='PENDING').count()
    approved_requests = SupplyRequest.objects.filter(status='APPROVED').count()
    rejected_requests = SupplyRequest.objects.filter(status='REJECTED').count()
    released_requests = SupplyRequest.objects.filter(status='RELEASED').count()

    # Department statistics
    department_stats = SupplyRequest.objects.values(
        'requester__userprofile__department'
    ).annotate(
        count=Count('id'),
        pending=Count('id', filter=Q(status='PENDING')),
        approved=Count('id', filter=Q(status='APPROVED')),
        rejected=Count('id', filter=Q(status='REJECTED')),
        released=Count('id', filter=Q(status='RELEASED'))
    ).order_by('-count')[:10]

    # Recent activity (last 7 days)
    week_ago = timezone.now() - timedelta(days=7)
    recent_requests = SupplyRequest.objects.filter(created_at__gte=week_ago).count()
    recent_approvals = SupplyRequest.objects.filter(approved_at__gte=week_ago).count()
    recent_releases = SupplyRequest.objects.filter(released_at__gte=week_ago).count()

    # Get unique departments and categories for filters
    departments = SupplyRequest.objects.values_list(
        'requester__userprofile__department', flat=True
    ).distinct().exclude(
        requester__userprofile__department__isnull=True
    ).exclude(
        requester__userprofile__department__exact=''
    ).order_by('requester__userprofile__department')

    categories = SupplyCategory.objects.all().order_by('name')

    # Pagination
    paginator = Paginator(requests, page_size)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'rejected_requests': rejected_requests,
        'released_requests': released_requests,
        'recent_requests': recent_requests,
        'recent_approvals': recent_approvals,
        'recent_releases': recent_releases,
        'department_stats': department_stats,
        'departments': departments,
        'categories': categories,

        # Filter values for form persistence
        'status_filter': status_filter,
        'department_filter': department_filter,
        'category_filter': category_filter,
        'priority_filter': priority_filter,
        'search_query': search_query,
        'sort_by': sort_by,
        'date_from': date_from,
        'date_to': date_to,
        'date_type': date_type,
        'page_size': page_size,
    }

    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'supply/gso/requests_list.html', context)

    return render(request, 'supply/gso/requests_dashboard.html', context)


@login_required
@role_required('GSO')
def bulk_request_operations(request):
    """Handle bulk operations on supply requests"""
    if request.method == 'POST':
        action = request.POST.get('action')
        request_ids = request.POST.getlist('selected_requests')

        if not request_ids:
            messages.error(request, 'No requests selected.')
            return redirect('supply:gso_requests_dashboard')

        # Convert to integers and validate
        try:
            request_ids = [int(id) for id in request_ids]
        except ValueError:
            messages.error(request, 'Invalid request IDs.')
            return redirect('supply:gso_requests_dashboard')

        # Get the requests
        requests_qs = SupplyRequest.objects.filter(id__in=request_ids)

        if action == 'bulk_approve':
            approval_notes = request.POST.get('approval_notes', '')
            approved_count = 0
            failed_count = 0

            for supply_request in requests_qs.filter(status='PENDING'):
                if supply_request.item.can_fulfill_quantity(supply_request.quantity):
                    if supply_request.approve(request.user, approval_notes):
                        approved_count += 1
                        # Log audit event
                        log_request_action(
                            user=request.user,
                            action_type='APPROVE',
                            supply_request=supply_request,
                            request=request,
                            additional_data={'bulk_operation': True, 'approval_notes': approval_notes}
                        )
                    else:
                        failed_count += 1
                else:
                    failed_count += 1

            if approved_count > 0:
                messages.success(request, f'Successfully approved {approved_count} request(s).')
            if failed_count > 0:
                messages.warning(request, f'{failed_count} request(s) could not be approved (insufficient stock or invalid status).')

        elif action == 'bulk_reject':
            rejection_notes = request.POST.get('rejection_notes', '')
            if not rejection_notes:
                messages.error(request, 'Rejection notes are required for bulk rejection.')
                return redirect('supply:gso_requests_dashboard')

            rejected_count = 0
            for supply_request in requests_qs.filter(status='PENDING'):
                if supply_request.reject(request.user, rejection_notes):
                    rejected_count += 1
                    # Log audit event
                    log_request_action(
                        user=request.user,
                        action_type='REJECT',
                        supply_request=supply_request,
                        request=request,
                        additional_data={'bulk_operation': True, 'rejection_notes': rejection_notes}
                    )

            if rejected_count > 0:
                messages.success(request, f'Successfully rejected {rejected_count} request(s).')

        elif action == 'bulk_release':
            release_notes = request.POST.get('release_notes', '')
            released_count = 0
            failed_count = 0

            for supply_request in requests_qs.filter(status='APPROVED'):
                if supply_request.item.can_fulfill_quantity(supply_request.quantity):
                    if supply_request.release(request.user, release_notes):
                        released_count += 1
                        # Log audit event
                        log_request_action(
                            user=request.user,
                            action_type='RELEASE',
                            supply_request=supply_request,
                            request=request,
                            additional_data={'bulk_operation': True, 'release_notes': release_notes}
                        )
                    else:
                        failed_count += 1
                else:
                    failed_count += 1

            if released_count > 0:
                messages.success(request, f'Successfully released {released_count} request(s).')
            if failed_count > 0:
                messages.warning(request, f'{failed_count} request(s) could not be released (insufficient stock or invalid status).')

        else:
            messages.error(request, 'Invalid action.')

    return redirect('supply:gso_requests_dashboard')


@login_required
@role_required('GSO')
def gso_dashboard(request):
    """GSO-specific dashboard with request management"""
    # Get filter parameters
    status_filter = request.GET.get('status', 'PENDING')
    department_filter = request.GET.get('department', '')
    search_query = request.GET.get('search', '')
    sort_by = request.GET.get('sort', '-created_at')
    
    # Base queryset - all requests for GSO
    requests = SupplyRequest.objects.select_related(
        'item', 'requester', 'requester__userprofile', 'approved_by', 'released_by'
    )
    
    # Apply filters
    if status_filter:
        requests = requests.filter(status=status_filter)
    
    if department_filter:
        requests = requests.filter(department__icontains=department_filter)
    
    if search_query:
        requests = requests.filter(
            Q(request_id__icontains=search_query) |
            Q(item__name__icontains=search_query) |
            Q(purpose__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query)
        )
    
    # Apply sorting
    valid_sort_fields = ['created_at', '-created_at', 'status', '-status', 'department', '-department', 'item__name', '-item__name']
    if sort_by in valid_sort_fields:
        requests = requests.order_by(sort_by)
    else:
        requests = requests.order_by('-created_at')
    
    # Pagination
    paginator = Paginator(requests, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get summary statistics
    total_requests = SupplyRequest.objects.count()
    pending_requests = SupplyRequest.objects.filter(status='PENDING').count()
    approved_requests = SupplyRequest.objects.filter(status='APPROVED').count()
    released_requests = SupplyRequest.objects.filter(status='RELEASED').count()
    rejected_requests = SupplyRequest.objects.filter(status='REJECTED').count()

    # Batch request statistics
    batch_requests = SupplyRequest.objects.filter(is_batch_request=True).count()
    single_requests = SupplyRequest.objects.filter(is_batch_request=False).count()
    pending_batch_requests = SupplyRequest.objects.filter(status='PENDING', is_batch_request=True).count()

    # User statistics
    from django.contrib.auth.models import User
    total_users = User.objects.filter(userprofile__role='DEPARTMENT').count()
    active_users = User.objects.filter(userprofile__role='DEPARTMENT', is_active=True).count()

    # Recent user registrations (last 30 days)
    from datetime import datetime, timedelta
    month_ago = datetime.now() - timedelta(days=30)
    recent_users = User.objects.filter(
        userprofile__role='DEPARTMENT',
        date_joined__gte=month_ago
    ).count()

    # Low stock items
    low_stock_items = SupplyItem.objects.filter(
        current_stock__lte=F('minimum_stock')
    ).count()

    # Recent activity (last 7 days)
    week_ago = datetime.now() - timedelta(days=7)
    recent_requests = SupplyRequest.objects.filter(created_at__gte=week_ago).count()

    # Get unique departments for filter dropdown
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'department_filter': department_filter,
        'search_query': search_query,
        'sort_by': sort_by,
        'departments': departments,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'released_requests': released_requests,
        'rejected_requests': rejected_requests,
        'batch_requests': batch_requests,
        'single_requests': single_requests,
        'pending_batch_requests': pending_batch_requests,
        'total_users': total_users,
        'active_users': active_users,
        'recent_users': recent_users,
        'low_stock_items': low_stock_items,
        'recent_requests': recent_requests,
        'status_choices': SupplyRequest.STATUS_CHOICES,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'supply/gso/dashboard_list.html', context)
    
    return render(request, 'supply/gso/dashboard.html', context)


@login_required
@role_required('GSO')
def inventory(request):
    """Enhanced inventory management dashboard with category support"""
    filter_form = CategoryFilterForm(request.GET)

    # Base queryset with category prefetch
    items = SupplyItem.objects.select_related('category').all()

    # Apply filters
    if filter_form.is_valid():
        category = filter_form.cleaned_data.get('category')
        search_query = filter_form.cleaned_data.get('search')
        stock_status = filter_form.cleaned_data.get('stock_status')

        if category:
            items = items.filter(category=category)

        if search_query:
            items = items.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        if stock_status == 'low_stock':
            items = items.filter(current_stock__lte=F('minimum_stock'))
        elif stock_status == 'out_of_stock':
            items = items.filter(current_stock=0)
        elif stock_status == 'in_stock':
            items = items.filter(current_stock__gt=F('minimum_stock'))

    # Order items for consistent display
    items = items.order_by('category__name', 'name')

    # Pagination
    paginator = Paginator(items, 20)  # Show 20 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Group items by category for display (only for current page)
    categories_with_items = {}
    uncategorized_items = []

    for item in page_obj:
        if item.category:
            if item.category not in categories_with_items:
                categories_with_items[item.category] = []
            categories_with_items[item.category].append(item)
        else:
            uncategorized_items.append(item)

    # Get statistics
    total_items = SupplyItem.objects.count()
    low_stock_count = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    out_of_stock_count = SupplyItem.objects.filter(current_stock=0).count()
    categories = SupplyCategory.objects.filter(is_active=True).order_by('name')

    # Category statistics
    category_stats = []
    for category in categories:
        category_items = category.items.all()
        category_stats.append({
            'category': category,
            'total_items': category_items.count(),
            'low_stock_items': category_items.filter(current_stock__lte=F('minimum_stock')).count(),
            'total_value': sum(item.current_stock * (item.unit_cost or 0) for item in category_items)
        })

    context = {
        'page_obj': page_obj,  # Add pagination object
        'categories_with_items': categories_with_items,
        'uncategorized_items': uncategorized_items,
        'filter_form': filter_form,
        'total_items': total_items,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
        'categories': categories,
        'category_stats': category_stats,
    }

    return render(request, 'supply/inventory/list.html', context)


@login_required
@role_required('GSO')
def release_management(request):
    """Enhanced release management dashboard for approved requests"""
    from datetime import datetime
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Get filter parameters with validation
        department_filter = request.GET.get('department', '').strip()
        search_query = request.GET.get('search', '').strip()
        sort_by = request.GET.get('sort', '-approved_at')
        stock_filter = request.GET.get('stock_filter', '')
        request_type_filter = request.GET.get('request_type', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')

        # Validate and sanitize inputs
        if len(search_query) > 100:
            search_query = search_query[:100]
            messages.warning(request, 'Search query was truncated to 100 characters.')

        if department_filter and len(department_filter) > 100:
            department_filter = department_filter[:100]
            messages.warning(request, 'Department filter was truncated.')

        # Validate stock filter
        valid_stock_filters = ['', 'available', 'insufficient', 'partial']
        if stock_filter not in valid_stock_filters:
            stock_filter = ''
            messages.warning(request, 'Invalid stock filter applied. Showing all requests.')

        # Validate request type filter
        valid_request_types = ['', 'single', 'batch']
        if request_type_filter not in valid_request_types:
            request_type_filter = ''
            messages.warning(request, 'Invalid request type filter applied. Showing all types.')

        # Base queryset - only approved requests
        requests = SupplyRequest.objects.filter(status='APPROVED').select_related(
            'item', 'requester', 'requester__userprofile', 'approved_by'
        ).prefetch_related('request_items__item')

        # Apply filters
        if department_filter:
            requests = requests.filter(department__icontains=department_filter)

        if search_query:
            requests = requests.filter(
                Q(request_id__icontains=search_query) |
                Q(item__name__icontains=search_query) |
                Q(purpose__icontains=search_query) |
                Q(requester__first_name__icontains=search_query) |
                Q(requester__last_name__icontains=search_query) |
                Q(requester__username__icontains=search_query) |
                Q(request_items__item__name__icontains=search_query)
            ).distinct()

        # New: Stock availability filter
        if stock_filter == 'available':
            # Filter requests that can be fully released
            available_requests = []
            for req in requests:
                if req.can_be_released():
                    available_requests.append(req.id)
            requests = requests.filter(id__in=available_requests)
        elif stock_filter == 'insufficient':
            # Filter requests with insufficient stock
            insufficient_requests = []
            for req in requests:
                if not req.can_be_released():
                    insufficient_requests.append(req.id)
            requests = requests.filter(id__in=insufficient_requests)
        elif stock_filter == 'partial':
            # Filter batch requests that can be partially released
            partial_requests = []
            for req in requests:
                if req.is_batch_request and req.can_partial_release() and not req.can_be_released():
                    partial_requests.append(req.id)
            requests = requests.filter(id__in=partial_requests)

        # New: Request type filter
        if request_type_filter == 'single':
            requests = requests.filter(is_batch_request=False)
        elif request_type_filter == 'batch':
            requests = requests.filter(is_batch_request=True)

        # New: Date range filter
        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                requests = requests.filter(approved_at__date__gte=from_date)
            except ValueError:
                pass

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                requests = requests.filter(approved_at__date__lte=to_date)
            except ValueError:
                pass

        # Apply sorting
        valid_sort_fields = [
            'approved_at', '-approved_at', 'department', '-department',
            'item__name', '-item__name', 'requester__last_name', '-requester__last_name'
        ]
        if sort_by in valid_sort_fields:
            requests = requests.order_by(sort_by)
        else:
            requests = requests.order_by('-approved_at')

        # Pagination with customizable page size
        page_size = int(request.GET.get('page_size', 12))
        page_size = min(max(page_size, 6), 24)  # Limit between 6 and 24
        paginator = Paginator(requests, page_size)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get unique departments for filter dropdown
        departments = SupplyRequest.objects.filter(status='APPROVED').values_list('department', flat=True).distinct().order_by('department')

        # Calculate enhanced stats
        total_approved = requests.count()
        available_count = sum(1 for req in page_obj if req.can_be_released())
        partial_count = sum(1 for req in page_obj if req.is_batch_request and req.can_partial_release() and not req.can_be_released())
        insufficient_count = sum(1 for req in page_obj if not req.can_be_released() and not (req.is_batch_request and req.can_partial_release()))

        context = {
            'page_obj': page_obj,
            'departments': departments,
            'department_filter': department_filter,
            'search_query': search_query,
            'sort_by': sort_by,
            'stock_filter': stock_filter,
            'request_type_filter': request_type_filter,
            'date_from': date_from,
            'date_to': date_to,
            'page_size': page_size,
            'total_approved': total_approved,
            'available_count': available_count,
            'partial_count': partial_count,
            'insufficient_count': insufficient_count,
        }

        # Return partial template for HTMX requests
        if request.headers.get('HX-Request'):
            return render(request, 'supply/gso/release_list.html', context)

        return render(request, 'supply/gso/release_management.html', context)

    except Exception as e:
        logger.error(f"Error in release_management view: {str(e)}", exc_info=True)
        messages.error(request, 'An error occurred while loading the release management page. Please try again.')

        # Return a minimal context for error recovery
        context = {
            'page_obj': [],
            'departments': [],
            'department_filter': '',
            'search_query': '',
            'sort_by': '-approved_at',
            'stock_filter': '',
            'request_type_filter': '',
            'date_from': '',
            'date_to': '',
            'page_size': 12,
            'total_approved': 0,
            'available_count': 0,
            'partial_count': 0,
            'insufficient_count': 0,
        }

        if request.headers.get('HX-Request'):
            return render(request, 'supply/gso/release_list.html', context)

        return render(request, 'supply/gso/release_management.html', context)


@login_required
@role_required('GSO')
def reports(request):
    """Main reports dashboard"""
    # Get date range parameters
    from datetime import datetime, timedelta
    from django.db.models import Count, Sum, Q

    end_date = request.GET.get('end_date')
    start_date = request.GET.get('start_date')

    # Default to last 30 days if no dates provided
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    if not start_date:
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    # Convert to datetime for filtering
    start_datetime = timezone.make_aware(datetime.combine(start_date, datetime.min.time()))
    end_datetime = timezone.make_aware(datetime.combine(end_date, datetime.max.time()))

    # Get summary statistics
    total_requests = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime]
    ).count()

    pending_requests = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime],
        status='PENDING'
    ).count()

    approved_requests = SupplyRequest.objects.filter(
        approved_at__range=[start_datetime, end_datetime],
        status='APPROVED'
    ).count()

    released_requests = SupplyRequest.objects.filter(
        released_at__range=[start_datetime, end_datetime],
        status='RELEASED'
    ).count()

    rejected_requests = SupplyRequest.objects.filter(
        approved_at__range=[start_datetime, end_datetime],
        status='REJECTED'
    ).count()

    # Department statistics
    dept_stats = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime]
    ).values('department').annotate(
        total_requests=Count('id'),
        approved_requests=Count('id', filter=Q(status__in=['APPROVED', 'RELEASED'])),
        released_requests=Count('id', filter=Q(status='RELEASED')),
        total_quantity=Sum('quantity', filter=Q(status='RELEASED'))
    ).order_by('-total_requests')[:10]

    # Most requested items
    item_stats = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime]
    ).values('item__name', 'item__unit').annotate(
        total_requests=Count('id'),
        total_quantity=Sum('quantity', filter=Q(status='RELEASED')),
        pending_quantity=Sum('quantity', filter=Q(status='PENDING')),
        approved_quantity=Sum('quantity', filter=Q(status='APPROVED'))
    ).order_by('-total_requests')[:10]

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'released_requests': released_requests,
        'rejected_requests': rejected_requests,
        'dept_stats': dept_stats,
        'item_stats': item_stats,
    }

    return render(request, 'supply/reports/dashboard.html', context)


@login_required
@role_required('GSO')
def requests_report(request):
    """Detailed requests report"""
    from datetime import datetime, timedelta

    # Get filter parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    export_format = request.GET.get('export', '')

    # Default to last 30 days if no dates provided
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    if not start_date:
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    # Convert to datetime for filtering
    start_datetime = timezone.make_aware(datetime.combine(start_date, datetime.min.time()))
    end_datetime = timezone.make_aware(datetime.combine(end_date, datetime.max.time()))

    # Base queryset
    requests = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime]
    ).select_related('item', 'requester', 'approved_by', 'released_by')

    # Apply filters
    if department:
        requests = requests.filter(department__icontains=department)

    # Get summary counts before status filter and pagination
    total_requests_count = requests.count()
    pending_count = requests.filter(status='Pending').count()
    approved_count = requests.filter(status='Approved').count()
    released_count = requests.filter(status='Released').count()

    if status:
        requests = requests.filter(status=status)

    requests = requests.order_by('-created_at')

    # Handle export
    if export_format in ['csv', 'pdf']:
        return _export_requests_report(requests, export_format, start_date, end_date)

    # Pagination for web view
    paginator = Paginator(requests, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get departments for filter
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')

    context = {
        'page_obj': page_obj,
        'start_date': start_date,
        'end_date': end_date,
        'department': department,
        'status': status,
        'departments': departments,
        'status_choices': SupplyRequest.STATUS_CHOICES,
        'total_requests': total_requests_count,
        'pending_count': pending_count,
        'approved_count': approved_count,
        'released_count': released_count,
    }

    return render(request, 'supply/reports/requests.html', context)


@login_required
@role_required('GSO')
def departmental_usage_report(request):
    """Departmental usage report"""
    from datetime import datetime, timedelta
    from django.db.models import Count, Sum, Q

    # Get filter parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    department = request.GET.get('department', '')
    export_format = request.GET.get('export', '')

    # Default to last 30 days if no dates provided
    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    if not start_date:
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    # Convert to datetime for filtering
    start_datetime = timezone.make_aware(datetime.combine(start_date, datetime.min.time()))
    end_datetime = timezone.make_aware(datetime.combine(end_date, datetime.max.time()))

    # Base queryset for departmental statistics
    dept_stats = SupplyRequest.objects.filter(
        created_at__range=[start_datetime, end_datetime]
    )

    if department:
        dept_stats = dept_stats.filter(department__icontains=department)

    dept_stats = dept_stats.values('department').annotate(
        total_requests=Count('id'),
        pending_requests=Count('id', filter=Q(status='PENDING')),
        approved_requests=Count('id', filter=Q(status__in=['APPROVED', 'RELEASED'])),
        released_requests=Count('id', filter=Q(status='RELEASED')),
        rejected_requests=Count('id', filter=Q(status='REJECTED')),
        total_quantity_released=Sum('quantity', filter=Q(status='RELEASED')),
        avg_approval_time=Count('id')  # This would need more complex calculation
    ).order_by('-total_requests')

    # Handle export
    if export_format in ['csv', 'pdf']:
        return _export_departmental_report(dept_stats, export_format, start_date, end_date)

    # Get departments for filter
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')

    context = {
        'dept_stats': dept_stats,
        'start_date': start_date,
        'end_date': end_date,
        'department': department,
        'departments': departments,
    }

    return render(request, 'supply/reports/departmental_usage.html', context)


@login_required
@role_required('GSO')
def inventory_report(request):
    """Inventory status report"""
    from django.db.models import Count, Sum, Q

    # Get filter parameters
    low_stock_only = request.GET.get('low_stock', False)
    search_query = request.GET.get('search', '')
    export_format = request.GET.get('export', '')

    # Base queryset
    items = SupplyItem.objects.all()

    if low_stock_only:
        items = items.filter(current_stock__lte=F('minimum_stock'))

    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Add transaction statistics
    items = items.annotate(
        total_transactions=Count('transactions'),
        total_out=Sum('transactions__quantity', filter=Q(transactions__transaction_type='OUT')),
        total_in=Sum('transactions__quantity', filter=Q(transactions__transaction_type='IN')),
        total_adjustments=Count('transactions', filter=Q(transactions__transaction_type='ADJUSTMENT'))
    ).order_by('name')

    # Handle export
    if export_format in ['csv', 'pdf']:
        return _export_inventory_report(items, export_format)

    # Pagination for web view
    paginator = Paginator(items, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Summary statistics
    total_items = SupplyItem.objects.count()
    low_stock_count = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    total_stock_value = SupplyItem.objects.aggregate(total=Sum('current_stock'))['total'] or 0

    context = {
        'page_obj': page_obj,
        'low_stock_only': low_stock_only,
        'search_query': search_query,
        'total_items': total_items,
        'low_stock_count': low_stock_count,
        'total_stock_value': total_stock_value,
    }

    return render(request, 'supply/reports/inventory.html', context)


@login_required
def global_search(request):
    """Global search functionality for requests and inventory items"""
    query = request.GET.get('q', '').strip()

    if not query:
        return JsonResponse({'results': []})

    results = []

    # Search in supply requests (if user has permission)
    if request.user.userprofile.role == 'GSO':
        # GSO can search all requests
        requests = SupplyRequest.objects.filter(
            Q(request_id__icontains=query) |
            Q(purpose__icontains=query) |
            Q(item__name__icontains=query) |
            Q(department__icontains=query) |
            Q(requester__first_name__icontains=query) |
            Q(requester__last_name__icontains=query)
        ).select_related('item', 'requester')[:10]

        for req in requests:
            results.append({
                'type': 'request',
                'id': req.id,
                'title': f'Request {req.request_id}',
                'subtitle': f'{req.item.name} - {req.department}',
                'status': req.get_status_display(),
                'url': reverse('supply:gso_request_detail', args=[req.id])
            })
    else:
        # Department users can only search their own requests
        requests = SupplyRequest.objects.filter(
            requester=request.user
        ).filter(
            Q(request_id__icontains=query) |
            Q(purpose__icontains=query) |
            Q(item__name__icontains=query)
        ).select_related('item')[:10]

        for req in requests:
            results.append({
                'type': 'request',
                'id': req.id,
                'title': f'Request {req.request_id}',
                'subtitle': f'{req.item.name}',
                'status': req.get_status_display(),
                'url': reverse('supply:request_detail', args=[req.id])
            })

    # Search in inventory items (if user has permission)
    if request.user.userprofile.role == 'GSO':
        items = SupplyItem.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        )[:10]

        for item in items:
            results.append({
                'type': 'item',
                'id': item.id,
                'title': item.name,
                'subtitle': f'{item.current_stock} {item.unit} available',
                'status': 'Low Stock' if item.current_stock <= item.minimum_stock else 'In Stock',
                'url': reverse('supply:inventory_detail', args=[item.id])
            })

    return JsonResponse({'results': results})


@login_required
def dashboard_widgets(request):
    """API endpoint for dashboard widgets data"""
    widget_type = request.GET.get('widget', '')

    if widget_type == 'recent_activity':
        # Get recent activity for the user
        if request.user.userprofile.role == 'GSO':
            # GSO sees all recent activity
            recent_requests = SupplyRequest.objects.select_related(
                'item', 'requester', 'approved_by', 'released_by'
            ).order_by('-created_at')[:10]
        else:
            # Department users see only their requests
            recent_requests = SupplyRequest.objects.filter(
                requester=request.user
            ).select_related('item', 'approved_by', 'released_by').order_by('-created_at')[:10]

        activity_data = []
        for req in recent_requests:
            activity_data.append({
                'id': req.id,
                'request_id': req.request_id,
                'item_name': req.item.name,
                'quantity': req.quantity,
                'unit': req.item.unit,
                'status': req.get_status_display(),
                'status_class': _get_status_class(req.status),
                'created_at': req.created_at.strftime('%b %d, %Y'),
                'department': req.department,
                'requester': req.requester.get_full_name() or req.requester.username,
            })

        return JsonResponse({'data': activity_data})

    elif widget_type == 'low_stock_alerts':
        # Only GSO can see low stock alerts
        if request.user.userprofile.role != 'GSO':
            return JsonResponse({'data': []})

        low_stock_items = SupplyItem.objects.filter(
            current_stock__lte=F('minimum_stock')
        ).order_by('current_stock')[:10]

        alerts_data = []
        for item in low_stock_items:
            alerts_data.append({
                'id': item.id,
                'name': item.name,
                'current_stock': item.current_stock,
                'minimum_stock': item.minimum_stock,
                'unit': item.unit,
                'urgency': 'critical' if item.current_stock == 0 else 'warning'
            })

        return JsonResponse({'data': alerts_data})

    elif widget_type == 'pending_approvals':
        # Only GSO can see pending approvals
        if request.user.userprofile.role != 'GSO':
            return JsonResponse({'data': []})

        pending_requests = SupplyRequest.objects.filter(
            status='PENDING'
        ).select_related('item', 'requester').order_by('created_at')[:10]

        pending_data = []
        for req in pending_requests:
            pending_data.append({
                'id': req.id,
                'request_id': req.request_id,
                'item_name': req.item.name,
                'quantity': req.quantity,
                'unit': req.item.unit,
                'requester': req.requester.get_full_name() or req.requester.username,
                'department': req.department,
                'created_at': req.created_at.strftime('%b %d, %Y'),
                'days_pending': (timezone.now().date() - req.created_at.date()).days
            })

        return JsonResponse({'data': pending_data})

    elif widget_type == 'statistics':
        # Get statistics based on user role
        if request.user.userprofile.role == 'GSO':
            # GSO sees system-wide statistics
            stats = {
                'total_requests': SupplyRequest.objects.count(),
                'pending_requests': SupplyRequest.objects.filter(status='PENDING').count(),
                'approved_requests': SupplyRequest.objects.filter(status='APPROVED').count(),
                'released_requests': SupplyRequest.objects.filter(status='RELEASED').count(),
                'total_items': SupplyItem.objects.count(),
                'low_stock_items': SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count(),
            }
        else:
            # Department users see their own statistics
            user_requests = SupplyRequest.objects.filter(requester=request.user)
            stats = {
                'total_requests': user_requests.count(),
                'pending_requests': user_requests.filter(status='PENDING').count(),
                'approved_requests': user_requests.filter(status='APPROVED').count(),
                'released_requests': user_requests.filter(status='RELEASED').count(),
            }

        return JsonResponse({'data': stats})

    return JsonResponse({'error': 'Invalid widget type'}, status=400)


def _get_status_class(status):
    """Helper function to get CSS class for status"""
    status_classes = {
        'PENDING': 'bg-yellow-100 text-yellow-800',
        'APPROVED': 'bg-blue-100 text-blue-800',
        'RELEASED': 'bg-green-100 text-green-800',
        'REJECTED': 'bg-red-100 text-red-800',
    }
    return status_classes.get(status, 'bg-gray-100 text-gray-800')


def _export_requests_report(requests, format_type, start_date, end_date):
    """Export requests report in CSV or PDF format"""
    import csv
    from django.http import HttpResponse

    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="requests_report_{start_date}_to_{end_date}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Request ID', 'Department', 'Requester', 'Item', 'Quantity', 'Unit',
            'Purpose', 'Status', 'Created Date', 'Approved Date', 'Released Date',
            'Approved By', 'Released By', 'Approval Remarks', 'Release Remarks'
        ])

        for req in requests:
            writer.writerow([
                req.request_id,
                req.department,
                req.requester.get_full_name() or req.requester.username,
                req.item.name,
                req.quantity,
                req.item.unit,
                req.purpose,
                req.get_status_display(),
                req.created_at.strftime('%Y-%m-%d %H:%M'),
                req.approved_at.strftime('%Y-%m-%d %H:%M') if req.approved_at else '',
                req.released_at.strftime('%Y-%m-%d %H:%M') if req.released_at else '',
                req.approved_by.get_full_name() if req.approved_by else '',
                req.released_by.get_full_name() if req.released_by else '',
                req.approval_remarks,
                req.release_remarks
            ])

        return response

    elif format_type == 'pdf':
        # For PDF export, we'll use a simple HTML to PDF approach
        # In production, you might want to use libraries like reportlab or weasyprint
        from django.template.loader import render_to_string
        from django.http import HttpResponse

        html_content = render_to_string('supply/reports/requests_pdf.html', {
            'requests': requests,
            'start_date': start_date,
            'end_date': end_date,
        })

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="requests_report_{start_date}_to_{end_date}.pdf"'

        # For now, return HTML content as PDF placeholder
        # In production, convert HTML to PDF using appropriate library
        response.write(html_content.encode('utf-8'))
        return response


def _export_departmental_report(dept_stats, format_type, start_date, end_date):
    """Export departmental usage report in CSV or PDF format"""
    import csv
    from django.http import HttpResponse

    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="departmental_usage_{start_date}_to_{end_date}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Department', 'Total Requests', 'Pending Requests', 'Approved Requests',
            'Released Requests', 'Rejected Requests', 'Total Quantity Released'
        ])

        for dept in dept_stats:
            writer.writerow([
                dept['department'],
                dept['total_requests'],
                dept['pending_requests'],
                dept['approved_requests'],
                dept['released_requests'],
                dept['rejected_requests'],
                dept['total_quantity_released'] or 0
            ])

        return response

    elif format_type == 'pdf':
        from django.template.loader import render_to_string
        from django.http import HttpResponse

        html_content = render_to_string('supply/reports/departmental_pdf.html', {
            'dept_stats': dept_stats,
            'start_date': start_date,
            'end_date': end_date,
        })

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="departmental_usage_{start_date}_to_{end_date}.pdf"'
        response.write(html_content.encode('utf-8'))
        return response


def _export_inventory_report(items, format_type):
    """Export inventory report in CSV or PDF format"""
    import csv
    from django.http import HttpResponse

    if format_type == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="inventory_report.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Item Name', 'Description', 'Unit', 'Current Stock', 'Minimum Stock',
            'Stock Status', 'Total Transactions', 'Total In', 'Total Out', 'Adjustments'
        ])

        for item in items:
            stock_status = 'Low Stock' if item.current_stock <= item.minimum_stock else 'Normal'
            writer.writerow([
                item.name,
                item.description,
                item.unit,
                item.current_stock,
                item.minimum_stock,
                stock_status,
                item.total_transactions or 0,
                item.total_in or 0,
                item.total_out or 0,
                item.total_adjustments or 0
            ])

        return response

    elif format_type == 'pdf':
        from django.template.loader import render_to_string
        from django.http import HttpResponse

        html_content = render_to_string('supply/reports/inventory_pdf.html', {
            'items': items,
        })

        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="inventory_report.pdf"'
        response.write(html_content.encode('utf-8'))
        return response


@login_required
@role_required('GSO')
def inventory_add(request):
    """Add new supply item"""
    if request.method == 'POST':
        form = SupplyItemForm(request.POST)
        if form.is_valid():
            item = form.save()
            
            # Create initial stock transaction if stock > 0
            if item.current_stock > 0:
                InventoryTransaction.objects.create(
                    item=item,
                    transaction_type='IN',
                    quantity=item.current_stock,
                    performed_by=request.user,
                    remarks=f"Initial stock for new item: {item.name}"
                )
            
            messages.success(request, f'Supply item "{item.name}" added successfully!')
            return redirect('supply:inventory')
    else:
        form = SupplyItemForm()
    
    return render(request, 'supply/inventory/add.html', {'form': form})


@login_required
@role_required('GSO')
def inventory_edit(request, item_id):
    """Edit supply item"""
    item = get_object_or_404(SupplyItem, id=item_id)
    
    if request.method == 'POST':
        old_stock = item.current_stock  # Capture old stock before form processing
        form = SupplyItemForm(request.POST, instance=item)
        if form.is_valid():
            updated_item = form.save()
            
            # Create adjustment transaction if stock changed
            if old_stock != updated_item.current_stock:
                quantity_diff = updated_item.current_stock - old_stock
                
                InventoryTransaction.objects.create(
                    item=updated_item,
                    transaction_type='ADJUSTMENT',
                    quantity=abs(quantity_diff),
                    performed_by=request.user,
                    remarks=f"Stock adjustment: {old_stock} → {updated_item.current_stock}"
                )
            
            messages.success(request, f'Supply item "{updated_item.name}" updated successfully!')
            return redirect('supply:inventory')
    else:
        form = SupplyItemForm(instance=item)
    
    return render(request, 'supply/inventory/edit.html', {'form': form, 'item': item})


@login_required
@role_required('GSO')
def inventory_detail(request, item_id):
    """View supply item details and transaction history"""
    item = get_object_or_404(SupplyItem, id=item_id)
    transactions = item.transactions.all().order_by('-created_at')
    
    # Pagination for transactions
    paginator = Paginator(transactions, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'item': item,
        'page_obj': page_obj,
    }
    
    return render(request, 'supply/inventory/detail.html', context)


@login_required
@role_required('GSO')
def inventory_adjust(request, item_id):
    """Adjust inventory stock levels"""
    item = get_object_or_404(SupplyItem, id=item_id)
    
    if request.method == 'POST':
        form = StockAdjustmentForm(request.POST, item=item)
        if form.is_valid():
            adjustment_type = form.cleaned_data['adjustment_type']
            quantity = form.cleaned_data['quantity']
            remarks = form.cleaned_data['remarks']
            
            with transaction.atomic():
                # Update item stock
                if adjustment_type == 'IN':
                    item.current_stock += quantity
                elif adjustment_type == 'OUT':
                    item.current_stock -= quantity
                elif adjustment_type == 'ADJUSTMENT':
                    # For adjustments, quantity represents the new total
                    old_stock = item.current_stock
                    item.current_stock = quantity
                    quantity = abs(quantity - old_stock)
                
                item.save()
                
                # Create transaction record
                InventoryTransaction.objects.create(
                    item=item,
                    transaction_type=adjustment_type,
                    quantity=quantity,
                    performed_by=request.user,
                    remarks=remarks
                )
            
            messages.success(request, f'Stock adjustment completed for "{item.name}"!')
            return redirect('supply:inventory_detail', item_id=item.id)
    else:
        form = StockAdjustmentForm(item=item)
    
    return render(request, 'supply/inventory/adjust.html', {'form': form, 'item': item})


@login_required
@role_required('GSO')
def inventory_delete(request, item_id):
    """Delete supply item"""
    item = get_object_or_404(SupplyItem, id=item_id)
    
    if request.method == 'POST':
        item_name = item.name
        item.delete()
        messages.success(request, f'Supply item "{item_name}" deleted successfully!')
        return redirect('supply:inventory')
    
    return render(request, 'supply/inventory/delete.html', {'item': item})


@login_required
@role_required('GSO')
def low_stock_alerts(request):
    """View low stock alerts"""
    low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).order_by('current_stock')
    
    context = {
        'low_stock_items': low_stock_items,
    }
    
    return render(request, 'supply/inventory/low_stock.html', context)


@login_required
@role_required('GSO')
def inventory_transactions(request):
    """View all inventory transactions"""
    transactions = InventoryTransaction.objects.select_related('item', 'performed_by').order_by('-created_at')
    
    # Filter by item if specified
    item_id = request.GET.get('item')
    if item_id:
        transactions = transactions.filter(item_id=item_id)
    
    # Filter by transaction type
    transaction_type = request.GET.get('type')
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)
    
    # Pagination
    paginator = Paginator(transactions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get items for filter dropdown
    items = SupplyItem.objects.all().order_by('name')
    
    context = {
        'page_obj': page_obj,
        'items': items,
        'selected_item': item_id,
        'selected_type': transaction_type,
        'transaction_types': InventoryTransaction.TRANSACTION_TYPES,
    }
    
    return render(request, 'supply/inventory/transactions.html', context)

# Supply Request Views

@login_required
def request_create(request):
    """Create a new supply request"""
    if request.method == 'POST':
        form = SupplyRequestForm(request.POST, user=request.user)
        if form.is_valid():
            supply_request = form.save()
            messages.success(request, f'Supply request {supply_request.request_id} submitted successfully!')
            
            # Return HTMX response if it's an HTMX request
            if request.headers.get('HX-Request'):
                return render(request, 'supply/requests/create_success.html', {
                    'request': supply_request
                })
            
            return redirect('supply:request_detail', request_id=supply_request.id)
    else:
        form = SupplyRequestForm(user=request.user)
    
    context = {
        'form': form,
        'available_items': SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
    }
    
    return render(request, 'supply/requests/create.html', context)


@login_required
def request_history(request):
    """View request history for department users"""
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')
    
    # Base queryset - only user's own requests for department users
    if hasattr(request.user, 'userprofile') and request.user.userprofile.role == 'GSO':
        requests = SupplyRequest.objects.all()
    else:
        requests = SupplyRequest.objects.filter(requester=request.user)
    
    # Apply filters
    if status_filter:
        requests = requests.filter(status=status_filter)
    
    if search_query:
        requests = requests.filter(
            Q(request_id__icontains=search_query) |
            Q(item__name__icontains=search_query) |
            Q(purpose__icontains=search_query)
        )
    
    requests = requests.select_related('item', 'requester', 'approved_by', 'released_by').order_by('-created_at')
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Create filter form
    filter_form = RequestFilterForm(initial={
        'status': status_filter,
        'search': search_query
    })

    # Calculate statistics for department users
    if not (hasattr(request.user, 'userprofile') and request.user.userprofile.role == 'GSO'):
        user_requests = SupplyRequest.objects.filter(requester=request.user)
        pending_count = user_requests.filter(status='PENDING').count()
        approved_count = user_requests.filter(status='APPROVED').count()
        released_count = user_requests.filter(status='RELEASED').count()
        rejected_count = user_requests.filter(status='REJECTED').count()
        total_count = user_requests.count()
    else:
        pending_count = approved_count = released_count = rejected_count = total_count = 0

    context = {
        'page_obj': page_obj,
        'filter_form': filter_form,
        'status_filter': status_filter,
        'search_query': search_query,
        'pending_count': pending_count,
        'approved_count': approved_count,
        'released_count': released_count,
        'rejected_count': rejected_count,
        'total_count': total_count,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'supply/requests/history_list.html', context)
    
    return render(request, 'supply/requests/history.html', context)


@login_required
def request_detail(request, request_id):
    """View detailed information about a supply request"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id)
    
    # Check permissions - users can only view their own requests unless they're GSO
    if (hasattr(request.user, 'userprofile') and request.user.userprofile.role != 'GSO' 
        and supply_request.requester != request.user):
        messages.error(request, "You don't have permission to view this request.")
        return redirect('supply:request_history')
    
    context = {
        'request': supply_request,
    }
    
    return render(request, 'supply/requests/detail.html', context)


@login_required
def item_info(request):
    """HTMX endpoint to get item information for request form"""
    item_id = request.GET.get('item')
    
    if item_id:
        try:
            item = SupplyItem.objects.get(id=item_id)
            context = {
                'item': item,
            }
            return render(request, 'supply/requests/item_info.html', context)
        except SupplyItem.DoesNotExist:
            pass
    
    return HttpResponse("")


@login_required
def request_status_update(request):
    """HTMX endpoint for real-time status updates"""
    request_ids = request.GET.get('request_ids', '').split(',')
    
    if request_ids and request_ids[0]:
        requests = SupplyRequest.objects.filter(id__in=request_ids).select_related('item')
        context = {
            'requests': requests,
        }
        return render(request, 'supply/requests/status_updates.html', context)
    
    return HttpResponse("")


@login_required
@role_required('GSO')
def gso_request_detail(request, request_id):
    """GSO view for detailed request information"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id)

    # Check if item exists and calculate inventory availability
    inventory_available = False
    item_missing = False

    if supply_request.is_batch_request:
        # For batch requests, check if there are any items
        if supply_request.request_items.exists():
            # Check if all items have sufficient stock
            inventory_available = all(
                item.item.can_fulfill_quantity(item.quantity)
                for item in supply_request.request_items.all()
            )
        else:
            item_missing = True  # Batch request with no items
    else:
        # For single item requests, use legacy logic
        if supply_request.item and supply_request.quantity:
            inventory_available = supply_request.item.can_fulfill_quantity(supply_request.quantity)
        elif not supply_request.item:
            item_missing = True

    context = {
        'request': supply_request,
        'can_approve': supply_request.status == 'PENDING' and not item_missing,
        'can_release': supply_request.status == 'APPROVED' and not item_missing,
        'inventory_available': inventory_available,
        'item_missing': item_missing,
    }

    return render(request, 'supply/gso/request_detail.html', context)


@login_required
@role_required('GSO')
def batch_operations(request):
    """Handle batch operations on multiple requests"""
    if request.method == 'POST':
        action = request.POST.get('action')
        request_ids = request.POST.getlist('selected_requests')
        
        if not request_ids:
            messages.error(request, 'No requests selected.')
            return redirect('supply:gso_dashboard')
        
        requests_to_process = SupplyRequest.objects.filter(id__in=request_ids)
        
        if action == 'bulk_approve':
            approved_count = 0
            failed_count = 0
            
            for req in requests_to_process:
                if (req.status == 'PENDING' and req.item and req.quantity and
                    req.item.can_fulfill_quantity(req.quantity)):
                    if req.approve(request.user, "Bulk approval"):
                        approved_count += 1
                    else:
                        failed_count += 1
                else:
                    failed_count += 1
            
            if approved_count > 0:
                messages.success(request, f'Successfully approved {approved_count} request(s).')
                # Log bulk approval
                approved_requests = [req for req in requests_to_process if req.status == 'APPROVED']
                log_bulk_operation(
                    user=request.user,
                    action_type='APPROVE',
                    objects=approved_requests,
                    request=request,
                    additional_data={'approval_remarks': 'Bulk approval'}
                )
            if failed_count > 0:
                messages.warning(request, f'Failed to approve {failed_count} request(s) due to insufficient stock or invalid status.')
        
        elif action == 'bulk_reject':
            rejection_reason = request.POST.get('rejection_reason', 'Bulk rejection')
            rejected_count = 0

            for req in requests_to_process:
                if req.status == 'PENDING':
                    if req.reject(request.user, rejection_reason):
                        rejected_count += 1

            if rejected_count > 0:
                messages.success(request, f'Successfully rejected {rejected_count} request(s).')

        elif action == 'bulk_release':
            release_notes = request.POST.get('release_notes', 'Bulk release')
            allow_partial = request.POST.get('allow_partial', 'false') == 'true'
            released_count = 0
            failed_count = 0
            partial_count = 0
            release_details = []

            for req in requests_to_process:
                if req.can_be_released() or (allow_partial and req.can_partial_release()):
                    release_result = req.release_with_tracking(
                        released_by=request.user,
                        remarks=release_notes,
                        partial_release=allow_partial
                    )

                    if release_result['success']:
                        # Send notification to department user
                        _send_release_notification(req)
                        released_count += 1

                        if release_result['details'].get('partial_release'):
                            partial_count += 1

                        release_details.append({
                            'request_id': req.request_id,
                            'success': True,
                            'details': release_result['details']
                        })
                    else:
                        failed_count += 1
                        release_details.append({
                            'request_id': req.request_id,
                            'success': False,
                            'error': release_result['message']
                        })
                else:
                    failed_count += 1
                    stock_status = req.get_stock_status()
                    release_details.append({
                        'request_id': req.request_id,
                        'success': False,
                        'error': f'Cannot release: {stock_status["message"]}'
                    })

            # Enhanced success/error messages
            if released_count > 0:
                success_msg = f'Successfully released {released_count} request(s).'
                if partial_count > 0:
                    success_msg += f' ({partial_count} partial releases)'
                messages.success(request, success_msg)

                # Log bulk release with detailed information
                log_bulk_operation(
                    user=request.user,
                    action_type='RELEASE',
                    objects=[req for req in requests_to_process if req.status == 'RELEASED'],
                    request=request,
                    additional_data={
                        'release_notes': release_notes,
                        'allow_partial': allow_partial,
                        'release_details': release_details
                    }
                )
            if failed_count > 0:
                messages.warning(request, f'Failed to release {failed_count} request(s) due to insufficient stock or invalid status.')

        # Return HTMX response if it's an HTMX request
        if request.headers.get('HX-Request'):
            if action == 'bulk_approve':
                processed_count = approved_count
                context = {
                    'action': action,
                    'processed_count': processed_count,
                    'failed_count': failed_count
                }
            elif action == 'bulk_reject':
                processed_count = rejected_count
                context = {
                    'action': action,
                    'processed_count': processed_count
                }
            elif action == 'bulk_release':
                processed_count = released_count
                context = {
                    'action': action,
                    'processed_count': processed_count,
                    'failed_count': failed_count,
                    'partial_count': partial_count,
                    'release_details': release_details
                }

            return render(request, 'supply/gso/enhanced_batch_success.html', context)
    
    return redirect('supply:gso_dashboard')


@login_required
@role_required('GSO')
def approve_request(request, request_id):
    """Approve a single supply request"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id)
    
    if request.method == 'POST':
        if supply_request.status == 'PENDING':
            remarks = request.POST.get('approval_remarks', '')
            
            if supply_request.item.can_fulfill_quantity(supply_request.quantity):
                if supply_request.approve(request.user, remarks):
                    messages.success(request, f'Request {supply_request.request_id} approved successfully.')

                    # Log audit event
                    log_request_action(
                        user=request.user,
                        action_type='APPROVE',
                        supply_request=supply_request,
                        request=request,
                        additional_data={'approval_remarks': remarks}
                    )

                    # Send notification to department user
                    _send_approval_notification(supply_request, 'approved')
                    
                    # Return HTMX response with updated page
                    if request.headers.get('HX-Request'):
                        return render(request, 'supply/gso/request_detail.html', {
                            'request': supply_request,
                            'can_approve': False,
                            'can_release': True,
                            'inventory_available': supply_request.item.can_fulfill_quantity(supply_request.quantity),
                        })
                else:
                    messages.error(request, 'Failed to approve request.')
            else:
                messages.error(request, 'Cannot approve request: insufficient stock available.')
        else:
            messages.error(request, 'Request cannot be approved in its current status.')
    
    # Return to request detail page
    return redirect('supply:gso_request_detail', request_id=request_id)


@login_required
@role_required('GSO')
def reject_request(request, request_id):
    """Reject a single supply request"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id)
    
    if request.method == 'POST':
        if supply_request.status == 'PENDING':
            remarks = request.POST.get('rejection_remarks', '')
            
            if not remarks.strip():
                messages.error(request, 'Rejection reason is required.')
                # Return error response for HTMX
                if request.headers.get('HX-Request'):
                    return render(request, 'supply/gso/approval_error.html', {
                        'error_message': 'Rejection reason is required.'
                    })
            else:
                if supply_request.reject(request.user, remarks):
                    messages.success(request, f'Request {supply_request.request_id} rejected.')

                    # Log audit event
                    log_request_action(
                        user=request.user,
                        action_type='REJECT',
                        supply_request=supply_request,
                        request=request,
                        additional_data={'rejection_remarks': remarks}
                    )

                    # Send notification to department user
                    _send_approval_notification(supply_request, 'rejected')
                    
                    # Return HTMX response with updated page
                    if request.headers.get('HX-Request'):
                        return render(request, 'supply/gso/request_detail.html', {
                            'request': supply_request,
                            'can_approve': False,
                            'can_release': False,
                            'inventory_available': supply_request.item.can_fulfill_quantity(supply_request.quantity),
                        })
                else:
                    messages.error(request, 'Failed to reject request.')
        else:
            messages.error(request, 'Request cannot be rejected in its current status.')
    
    # Return to request detail page
    return redirect('supply:gso_request_detail', request_id=request_id)


def _render_release_list_htmx(request):
    """Helper function to render the release list for HTMX responses"""
    # Create a copy of the request with HX-Request header to ensure HTMX response
    request.META['HX-Request'] = 'true'

    # Call the main release_management view which already handles HTMX correctly
    return release_management(request)


@login_required
@role_required('GSO')
def release_request(request, request_id):
    """Enhanced release supplies for an approved request"""
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Validate request_id
        try:
            request_id = int(request_id)
        except (ValueError, TypeError):
            messages.error(request, 'Invalid request ID provided.')
            return redirect('supply:release_management')

        supply_request = get_object_or_404(SupplyRequest, id=request_id)

        # Additional security check
        if supply_request.status != 'APPROVED':
            messages.error(request, 'Only approved requests can be released.')
            return redirect('supply:release_management')

        if request.method == 'POST':
            if supply_request.status == 'APPROVED':
                remarks = request.POST.get('release_remarks', '')
                allow_partial = request.POST.get('allow_partial', 'false') == 'true'

                # Use the enhanced release method with detailed tracking
                if supply_request.can_be_released() or (allow_partial and supply_request.can_partial_release()):
                    release_result = supply_request.release_with_tracking(
                        released_by=request.user,
                        remarks=remarks,
                        partial_release=allow_partial
                    )

                    if release_result['success']:
                        success_msg = release_result['message']
                        if release_result['details'].get('partial_release'):
                            success_msg += f" (Partial release: {len(release_result['details']['released_items'])} items released)"

                        messages.success(request, success_msg)

                        # Log audit event with detailed information
                        log_request_action(
                            user=request.user,
                            action_type='RELEASE',
                            supply_request=supply_request,
                            request=request,
                            additional_data={
                                'release_remarks': remarks,
                                'released_items': release_result['details']['released_items'],
                                'failed_items': release_result['details']['failed_items'],
                                'partial_release': release_result['details']['partial_release']
                            }
                        )

                        # Send notification to department user
                        _send_release_notification(supply_request)

                        # Return HTMX response with updated page
                        if request.headers.get('HX-Request'):
                            # Check if the request came from the release management page
                            referer = request.META.get('HTTP_REFERER', '')
                            if 'releases' in referer:
                                # Return enhanced success modal for release management page
                                return render(request, 'supply/gso/enhanced_release_success_modal.html', {
                                    'success_message': success_msg,
                                    'release_details': release_result['details'],
                                    'supply_request': supply_request
                                })
                            else:
                                # Return updated request detail for individual request pages
                                return render(request, 'supply/gso/request_detail.html', {
                                    'request': supply_request,
                                    'can_approve': False,
                                    'can_release': False,
                                'inventory_available': supply_request.can_be_released(),
                                'item_missing': supply_request.is_batch_request and not supply_request.request_items.exists(),
                            })
                else:
                    error_msg = release_result['message']
                    messages.error(request, error_msg)
                    # Return error response for HTMX
                    if request.headers.get('HX-Request'):
                        # Check if the request came from the release management page
                        referer = request.META.get('HTTP_REFERER', '')
                        if 'releases' in referer:
                            # Return enhanced error modal for release management page
                            return render(request, 'supply/gso/enhanced_release_error_modal.html', {
                                'error_message': error_msg,
                                'error_details': release_result.get('details', {}),
                                'supply_request': supply_request
                            })
                        else:
                            return render(request, 'supply/gso/release_error.html', {
                                'error_message': error_msg,
                                'error_details': release_result.get('details', {})
                            })
            else:
                # Get detailed error message using the enhanced method
                stock_status = supply_request.get_stock_status()
                error_msg = f'Cannot release supplies: {stock_status["message"]}'
                messages.error(request, error_msg)
                # Return error response for HTMX
                if request.headers.get('HX-Request'):
                    # Check if the request came from the release management page
                    referer = request.META.get('HTTP_REFERER', '')
                    if 'releases' in referer:
                        # Return enhanced error modal for release management page
                        return render(request, 'supply/gso/enhanced_release_error_modal.html', {
                            'error_message': error_msg,
                            'stock_status': stock_status,
                            'supply_request': supply_request
                        })
                    else:
                        return render(request, 'supply/gso/release_error.html', {
                            'error_message': error_msg,
                            'stock_status': stock_status
                        })
        else:
            error_msg = 'Request must be approved before supplies can be released.'
            messages.error(request, error_msg)
            # Return error response for HTMX
            if request.headers.get('HX-Request'):
                # Check if the request came from the release management page
                referer = request.META.get('HTTP_REFERER', '')
                if 'releases' in referer:
                    # Return updated release list with error message
                    return _render_release_list_htmx(request)
                else:
                    return render(request, 'supply/gso/release_error.html', {
                        'error_message': error_msg
                    })

        # Return to request detail page
        return redirect('supply:gso_request_detail', request_id=request_id)

    except Exception as e:
        logger.error(f"Error in release_request view for request {request_id}: {str(e)}", exc_info=True)
        messages.error(request, 'An unexpected error occurred while processing the release. Please try again.')

        # Return appropriate error response
        if request.headers.get('HX-Request'):
            referer = request.META.get('HTTP_REFERER', '')
            if 'releases' in referer:
                return render(request, 'supply/gso/enhanced_release_error_modal.html', {
                    'error_message': 'An unexpected error occurred while processing the release.',
                    'error_details': {'error': str(e)}
                })
            else:
                return render(request, 'supply/gso/release_error.html', {
                    'error_message': 'An unexpected error occurred while processing the release.',
                    'error_details': {'error': str(e)}
                })

        return redirect('supply:release_management')


def _send_approval_notification(supply_request, action):
    """Send notification to department user about request status change"""
    try:
        subject = f'Supply Request {supply_request.request_id} {action.title()}'

        if action == 'approved':
            message = f"""
Dear {supply_request.requester.get_full_name() or supply_request.requester.username},

Your supply request has been approved.

Request Details:
- Request ID: {supply_request.request_id}
- Item: {supply_request.item.name}
- Quantity: {supply_request.quantity} {supply_request.item.unit}
- Purpose: {supply_request.purpose}
- Approved by: {supply_request.approved_by.get_full_name() or supply_request.approved_by.username}
- Approved on: {supply_request.approved_at.strftime('%B %d, %Y at %I:%M %p')}

{f'Remarks: {supply_request.approval_remarks}' if supply_request.approval_remarks else ''}

Your supplies will be released soon. You will receive another notification when they are ready for pickup.

Best regards,
General Services Office
Municipal Supply Request and Release Management System
            """
        elif action == 'rejected':
            message = f"""
Dear {supply_request.requester.get_full_name() or supply_request.requester.username},

Your supply request has been rejected.

Request Details:
- Request ID: {supply_request.request_id}
- Item: {supply_request.item.name}
- Quantity: {supply_request.quantity} {supply_request.item.unit}
- Purpose: {supply_request.purpose}
- Rejected by: {supply_request.approved_by.get_full_name() or supply_request.approved_by.username}
- Rejected on: {supply_request.approved_at.strftime('%B %d, %Y at %I:%M %p')}

Reason for rejection: {supply_request.approval_remarks}

If you have questions about this decision, please contact the General Services Office.

Best regards,
General Services Office
Municipal Supply Request and Release Management System
            """

        # Send email notification if email is configured
        if hasattr(settings, 'EMAIL_HOST') and supply_request.requester.email:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                [supply_request.requester.email],
                fail_silently=True,
            )

        # For now, we'll just add a message that can be displayed in the interface
        # In a real system, this could be stored in a notifications table

    except Exception as e:
        # Log the error but don't fail the approval process
        print(f"Failed to send notification: {e}")


def _send_release_notification(supply_request):
    """Send notification to department user about supply release"""
    try:
        subject = f'Supply Request {supply_request.request_id} - Supplies Released'

        message = f"""
Dear {supply_request.requester.get_full_name() or supply_request.requester.username},

Your requested supplies have been released and are ready for pickup.

Request Details:
- Request ID: {supply_request.request_id}
- Item: {supply_request.item.name}
- Quantity: {supply_request.quantity} {supply_request.item.unit}
- Purpose: {supply_request.purpose}
- Released by: {supply_request.released_by.get_full_name() or supply_request.released_by.username}
- Released on: {supply_request.released_at.strftime('%B %d, %Y at %I:%M %p')}

{f'Release Notes: {supply_request.release_remarks}' if supply_request.release_remarks else ''}

Please coordinate with the General Services Office to arrange pickup of your supplies.

Best regards,
General Services Office
Municipal Supply Request and Release Management System
        """

        # Send email notification if email is configured
        if hasattr(settings, 'EMAIL_HOST') and supply_request.requester.email:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL if hasattr(settings, 'DEFAULT_FROM_EMAIL') else '<EMAIL>',
                [supply_request.requester.email],
                fail_silently=True,
            )

        # For now, we'll just add a message that can be displayed in the interface
        # In a real system, this could be stored in a notifications table

    except Exception as e:
        # Log the error but don't fail the release process
        print(f"Failed to send release notification: {e}")


def landing_page(request):
    """Landing page for non-authenticated users"""
    if request.user.is_authenticated:
        return redirect('supply:dashboard')
    return render(request, 'landing.html')


def custom_logout(request):
    """Custom logout view that handles both GET and POST requests"""
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    return redirect('supply:landing_page')


@login_required
def request_create_batch(request):
    """Create batch supply requests"""
    if request.method == 'POST':
        batch_form = BatchRequestForm(request.POST)
        selected_items_json = request.POST.get('selected_items', '[]')

        try:
            import json
            selected_items = json.loads(selected_items_json)
        except (json.JSONDecodeError, TypeError):
            selected_items = []

        if batch_form.is_valid() and selected_items:
            purpose = batch_form.cleaned_data['purpose']
            save_as_draft = request.POST.get('save_as_draft') == 'true'

            try:
                with transaction.atomic():
                    # Create a single batch request
                    batch_request = SupplyRequest.objects.create(
                        requester=request.user,
                        department=request.user.userprofile.department if hasattr(request.user, 'userprofile') else 'Unknown',
                        purpose=purpose,
                        status='DRAFT' if save_as_draft else 'PENDING',
                        is_batch_request=True
                    )

                    # Add all selected items to the batch request
                    from .models import SupplyRequestItem
                    items_created = 0
                    for item_data in selected_items:
                        item = get_object_or_404(SupplyItem, id=item_data['id'])
                        quantity = int(item_data['quantity'])

                        # Validate quantity
                        if quantity <= 0:
                            raise ValueError(f"Invalid quantity for {item.name}")
                        if quantity > item.current_stock:
                            raise ValueError(f"Requested quantity ({quantity}) exceeds available stock ({item.current_stock}) for {item.name}")

                        # Create the request item
                        SupplyRequestItem.objects.create(
                            request=batch_request,
                            item=item,
                            quantity=quantity
                        )
                        items_created += 1

                    # Ensure at least one item was created
                    if items_created == 0:
                        raise ValueError("No valid items were added to the batch request")

                if save_as_draft:
                    messages.success(request, f'Successfully saved batch request with {len(selected_items)} items as draft.')
                else:
                    messages.success(request, f'Successfully submitted batch request with {len(selected_items)} items.')

                return redirect('supply:request_history')

            except ValueError as e:
                messages.error(request, str(e))
            except Exception as e:
                messages.error(request, 'An error occurred while processing your request. Please try again.')

        elif not selected_items:
            messages.error(request, 'Please select at least one item before submitting.')
    else:
        batch_form = BatchRequestForm()

    # Get search form and items
    search_form = ItemSearchForm(request.GET)
    items_queryset = SupplyItem.objects.all()

    # Apply filters
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        category = search_form.cleaned_data.get('category')
        in_stock_only = search_form.cleaned_data.get('in_stock_only')

        if search_query:
            items_queryset = items_queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        if category:
            # Since we don't have a category field in the model, we'll simulate it
            # by filtering based on item names that contain category keywords
            category_keywords = {
                'Office Supplies': ['paper', 'pen', 'pencil', 'stapler', 'folder', 'clip', 'tape', 'glue'],
                'Computer Supplies': ['ink', 'toner', 'cd', 'dvd', 'usb', 'mouse', 'keyboard'],
                'Cleaning Supplies': ['toilet', 'soap', 'detergent', 'wax', 'disinfectant', 'bleach', 'trash', 'broom', 'mop'],
                'Medical Supplies': ['first aid', 'bandage', 'gauze', 'tape', 'antiseptic', 'thermometer', 'gloves', 'mask', 'sanitizer'],
                'Maintenance Supplies': ['bulb', 'cord', 'tape', 'screwdriver', 'hammer', 'pliers', 'nail', 'screw', 'oil', 'brush'],
                'Furniture & Equipment': ['chair', 'table', 'cabinet', 'shelf', 'board', 'fan', 'dispenser', 'microwave', 'refrigerator']
            }

            if category in category_keywords:
                keywords = category_keywords[category]
                q_objects = Q()
                for keyword in keywords:
                    q_objects |= Q(name__icontains=keyword)
                items_queryset = items_queryset.filter(q_objects)

        if in_stock_only:
            items_queryset = items_queryset.filter(current_stock__gt=0)

    items = items_queryset.order_by('name')

    # Get recently requested items for this user
    recent_requests = SupplyRequest.objects.filter(
        requester=request.user
    ).order_by('-created_at')[:10]
    recent_items = [req.item.id for req in recent_requests if req.item]

    context = {
        'batch_form': batch_form,
        'search_form': search_form,
        'items': items,
        'recent_items': recent_items,
    }

    return render(request, 'supply/requests/create_batch.html', context)


@login_required
def search_items(request):
    """HTMX endpoint for searching items"""
    search_form = ItemSearchForm(request.GET)
    items_queryset = SupplyItem.objects.all()

    # Apply filters
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        category = search_form.cleaned_data.get('category')
        in_stock_only = search_form.cleaned_data.get('in_stock_only')

        if search_query:
            items_queryset = items_queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        if category:
            # Category filtering based on keywords
            category_keywords = {
                'Office Supplies': ['paper', 'pen', 'pencil', 'stapler', 'folder', 'clip', 'tape', 'glue'],
                'Computer Supplies': ['ink', 'toner', 'cd', 'dvd', 'usb', 'mouse', 'keyboard'],
                'Cleaning Supplies': ['toilet', 'soap', 'detergent', 'wax', 'disinfectant', 'bleach', 'trash', 'broom', 'mop'],
                'Medical Supplies': ['first aid', 'bandage', 'gauze', 'tape', 'antiseptic', 'thermometer', 'gloves', 'mask', 'sanitizer'],
                'Maintenance Supplies': ['bulb', 'cord', 'tape', 'screwdriver', 'hammer', 'pliers', 'nail', 'screw', 'oil', 'brush'],
                'Furniture & Equipment': ['chair', 'table', 'cabinet', 'shelf', 'board', 'fan', 'dispenser', 'microwave', 'refrigerator']
            }

            if category in category_keywords:
                keywords = category_keywords[category]
                q_objects = Q()
                for keyword in keywords:
                    q_objects |= Q(name__icontains=keyword)
                items_queryset = items_queryset.filter(q_objects)

        if in_stock_only:
            items_queryset = items_queryset.filter(current_stock__gt=0)

    items = items_queryset.order_by('name')

    # Get recently requested items for this user
    recent_requests = SupplyRequest.objects.filter(
        requester=request.user
    ).order_by('-created_at')[:10]
    recent_items = [req.item.id for req in recent_requests if req.item]

    context = {
        'items': items,
        'recent_items': recent_items,
    }

    return render(request, 'supply/requests/items_grid.html', context)


@login_required
@role_required('GSO')
def user_management(request):
    """User management interface for GSO administrators"""
    search_form = UserSearchForm(request.GET)
    users_queryset = User.objects.filter(userprofile__role='DEPARTMENT').select_related('userprofile')

    # Apply filters
    if search_form.is_valid():
        search_query = search_form.cleaned_data.get('search')
        department_filter = search_form.cleaned_data.get('department')
        status_filter = search_form.cleaned_data.get('status')

        if search_query:
            users_queryset = users_queryset.filter(
                Q(username__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(userprofile__department__icontains=search_query)
            )

        if department_filter:
            users_queryset = users_queryset.filter(userprofile__department__icontains=department_filter)

        if status_filter == 'active':
            users_queryset = users_queryset.filter(is_active=True)
        elif status_filter == 'inactive':
            users_queryset = users_queryset.filter(is_active=False)

    # Pagination
    paginator = Paginator(users_queryset, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_users = User.objects.filter(userprofile__role='DEPARTMENT').count()
    active_users = User.objects.filter(userprofile__role='DEPARTMENT', is_active=True).count()
    inactive_users = total_users - active_users

    # Recent registrations (last 30 days)
    from datetime import datetime, timedelta
    month_ago = datetime.now() - timedelta(days=30)
    recent_registrations = User.objects.filter(
        userprofile__role='DEPARTMENT',
        date_joined__gte=month_ago
    ).count()

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_users': total_users,
        'active_users': active_users,
        'inactive_users': inactive_users,
        'recent_registrations': recent_registrations,
    }

    return render(request, 'supply/gso/user_management.html', context)


@login_required
@role_required('GSO')
def user_create(request):
    """Create new department user"""
    if request.method == 'POST':
        form = UserManagementForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create user
                    user = form.save(commit=False)
                    if form.cleaned_data['password']:
                        user.set_password(form.cleaned_data['password'])
                    else:
                        user.set_password('municipal123')  # Default password
                    user.save()

                    # Create user profile
                    UserProfile.objects.create(
                        user=user,
                        role='DEPARTMENT',
                        department=form.cleaned_data['department'],
                        phone=form.cleaned_data.get('phone', '')
                    )

                    # Log audit event
                    log_audit_event(
                        user=request.user,
                        action_type='CREATE',
                        object_type='User',
                        object_id=str(user.id),
                        object_repr=str(user),
                        changes={'created_user': user.username, 'department': form.cleaned_data['department']},
                        request=request
                    )

                    messages.success(request, f'User {user.username} created successfully.')
                    return redirect('supply:user_management')

            except Exception as e:
                messages.error(request, f'Error creating user: {str(e)}')
    else:
        form = UserManagementForm()

    context = {
        'form': form,
        'action': 'Create',
        'submit_url': reverse('supply:user_create')
    }

    return render(request, 'supply/gso/user_form.html', context)


@login_required
@role_required('GSO')
def user_edit(request, user_id):
    """Edit existing department user"""
    user = get_object_or_404(User, id=user_id, userprofile__role='DEPARTMENT')

    if request.method == 'POST':
        form = UserManagementForm(request.POST, instance=user)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Track changes
                    old_data = {
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_active': user.is_active,
                        'department': user.userprofile.department if hasattr(user, 'userprofile') else '',
                        'phone': user.userprofile.phone if hasattr(user, 'userprofile') else ''
                    }

                    # Update user
                    user = form.save(commit=False)
                    if form.cleaned_data['password']:
                        user.set_password(form.cleaned_data['password'])
                    user.save()

                    # Update profile
                    if hasattr(user, 'userprofile'):
                        user.userprofile.department = form.cleaned_data['department']
                        user.userprofile.phone = form.cleaned_data.get('phone', '')
                        user.userprofile.save()

                    # Track changes
                    new_data = {
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_active': user.is_active,
                        'department': user.userprofile.department,
                        'phone': user.userprofile.phone
                    }

                    changes = {k: {'old': old_data[k], 'new': new_data[k]}
                              for k in old_data if old_data[k] != new_data[k]}

                    # Log audit event
                    log_audit_event(
                        user=request.user,
                        action_type='UPDATE',
                        object_type='User',
                        object_id=str(user.id),
                        object_repr=str(user),
                        changes=changes,
                        request=request
                    )

                    messages.success(request, f'User {user.username} updated successfully.')
                    return redirect('supply:user_management')

            except Exception as e:
                messages.error(request, f'Error updating user: {str(e)}')
    else:
        # Pre-populate form with existing data
        initial_data = {
            'department': user.userprofile.department if hasattr(user, 'userprofile') else '',
            'phone': user.userprofile.phone if hasattr(user, 'userprofile') else ''
        }
        form = UserManagementForm(instance=user, initial=initial_data)

    context = {
        'form': form,
        'user_obj': user,
        'action': 'Edit',
        'submit_url': reverse('supply:user_edit', args=[user.id])
    }

    return render(request, 'supply/gso/user_form.html', context)


@login_required
@role_required('GSO')
def user_toggle_status(request, user_id):
    """Toggle user active/inactive status"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id, userprofile__role='DEPARTMENT')

        old_status = user.is_active
        user.is_active = not user.is_active
        user.save()

        # Log audit event
        log_audit_event(
            user=request.user,
            action_type='UPDATE',
            object_type='User',
            object_id=str(user.id),
            object_repr=str(user),
            changes={'is_active': {'old': old_status, 'new': user.is_active}},
            request=request
        )

        status_text = 'activated' if user.is_active else 'deactivated'
        messages.success(request, f'User {user.username} has been {status_text}.')

    return redirect('supply:user_management')


@login_required
@role_required('GSO')
def user_reset_password(request, user_id):
    """Reset user password"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id, userprofile__role='DEPARTMENT')

        # Reset to default password
        user.set_password('municipal123')
        user.save()

        # Log audit event
        log_audit_event(
            user=request.user,
            action_type='UPDATE',
            object_type='User',
            object_id=str(user.id),
            object_repr=str(user),
            changes={'password_reset': True},
            request=request
        )

        messages.success(request, f'Password reset for {user.username}. New password: municipal123')

    return redirect('supply:user_management')


@login_required
@role_required('GSO')
def user_delete(request, user_id):
    """Delete user account"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id, userprofile__role='DEPARTMENT')

        # Check if user has any requests
        request_count = SupplyRequest.objects.filter(requester=user).count()

        if request_count > 0:
            messages.error(request, f'Cannot delete user {user.username}. User has {request_count} supply requests.')
        else:
            username = user.username

            # Log audit event before deletion
            log_audit_event(
                user=request.user,
                action_type='DELETE',
                object_type='User',
                object_id=str(user.id),
                object_repr=str(user),
                changes={'deleted_user': username},
                request=request
            )

            user.delete()
            messages.success(request, f'User {username} has been deleted.')

    return redirect('supply:user_management')


@login_required
@role_required('GSO')
def batch_request_detail(request, request_id):
    """Enhanced request detail view for batch requests"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id)

    if request.method == 'POST':
        if supply_request.is_batch_request:
            # Handle batch request approval
            action = request.POST.get('action')
            remarks = request.POST.get('remarks', '')

            if action == 'approve_all':
                supply_request.status = 'APPROVED'
                supply_request.approved_by = request.user
                supply_request.approved_at = timezone.now()
                supply_request.approval_remarks = remarks
                supply_request.save()

                # Update all items in the batch
                for request_item in supply_request.request_items.all():
                    request_item.approved_quantity = request_item.quantity
                    request_item.remarks = remarks
                    request_item.save()

                messages.success(request, 'Batch request approved successfully.')

            elif action == 'reject_all':
                supply_request.status = 'REJECTED'
                supply_request.approved_by = request.user
                supply_request.approved_at = timezone.now()
                supply_request.approval_remarks = remarks
                supply_request.save()

                # Update all items in the batch
                for request_item in supply_request.request_items.all():
                    request_item.approved_quantity = 0
                    request_item.remarks = remarks
                    request_item.save()

                messages.success(request, 'Batch request rejected.')

            elif action == 'individual':
                # Handle individual item approvals
                all_approved = True
                any_approved = False

                for request_item in supply_request.request_items.all():
                    item_action = request.POST.get(f'item_action_{request_item.id}')
                    item_quantity = request.POST.get(f'item_quantity_{request_item.id}')
                    item_remarks = request.POST.get(f'item_remarks_{request_item.id}', '')

                    if item_action == 'approve':
                        approved_qty = int(item_quantity) if item_quantity else request_item.quantity
                        request_item.approved_quantity = min(approved_qty, request_item.quantity)
                        any_approved = True
                    else:
                        request_item.approved_quantity = 0
                        all_approved = False

                    request_item.remarks = item_remarks
                    request_item.save()

                # Update overall request status
                if all_approved and any_approved:
                    supply_request.status = 'APPROVED'
                    supply_request.approved_by = request.user
                    supply_request.approved_at = timezone.now()
                    supply_request.approval_remarks = remarks
                elif any_approved:
                    supply_request.status = 'APPROVED'  # Partial approval
                    supply_request.approved_by = request.user
                    supply_request.approved_at = timezone.now()
                    supply_request.approval_remarks = remarks
                else:
                    supply_request.status = 'REJECTED'
                    supply_request.approved_by = request.user
                    supply_request.approved_at = timezone.now()
                    supply_request.approval_remarks = remarks

                supply_request.save()
                messages.success(request, 'Individual item approvals processed.')

            # Log audit event
            log_request_action(
                user=request.user,
                action_type=supply_request.status,
                supply_request=supply_request,
                request=request,
                additional_data={'approval_remarks': remarks}
            )

            return redirect('supply:gso_dashboard')

        else:
            # Handle single item request (existing logic)
            action = request.POST.get('action')
            remarks = request.POST.get('remarks', '')

            if action in ['APPROVED', 'REJECTED']:
                supply_request.status = action
                supply_request.save()

                # Log the action
                log_request_action(
                    user=request.user,
                    action_type=action,
                    supply_request=supply_request,
                    request=request,
                    additional_data={'approval_remarks': remarks}
                )

                messages.success(request, f'Request {action.lower()} successfully.')
                return redirect('supply:gso_dashboard')

    # Calculate approval context for batch requests
    inventory_available = False
    item_missing = False

    if supply_request.is_batch_request:
        # For batch requests, check if there are any items
        if supply_request.request_items.exists():
            # Check if all items have sufficient stock
            inventory_available = all(
                item.item.can_fulfill_quantity(item.quantity)
                for item in supply_request.request_items.all()
            )
        else:
            item_missing = True  # Batch request with no items

    context = {
        'request': supply_request,
        'is_batch': supply_request.is_batch_request,
        'can_approve': supply_request.status == 'PENDING' and not item_missing,
        'can_release': supply_request.status == 'APPROVED' and not item_missing,
        'inventory_available': inventory_available,
        'item_missing': item_missing,
    }

    return render(request, 'supply/gso/request_detail.html', context)


@login_required
@role_required('GSO')
def category_management(request):
    """Category management interface for GSO administrators"""
    categories = SupplyCategory.objects.all().order_by('name')

    context = {
        'categories': categories,
    }

    return render(request, 'supply/inventory/category_management.html', context)


@login_required
@role_required('GSO')
def category_create(request):
    """Create new supply category"""
    if request.method == 'POST':
        form = SupplyCategoryForm(request.POST)
        if form.is_valid():
            category = form.save()

            # Log audit event
            log_audit_event(
                user=request.user,
                action_type='CREATE',
                object_type='SupplyCategory',
                object_id=str(category.id),
                object_repr=str(category),
                changes={'created_category': category.name},
                request=request
            )

            messages.success(request, f'Category "{category.name}" created successfully.')
            return redirect('supply:category_management')
    else:
        form = SupplyCategoryForm()

    context = {
        'form': form,
        'action': 'Create',
        'submit_url': reverse('supply:category_create')
    }

    return render(request, 'supply/inventory/category_form.html', context)


@login_required
@role_required('GSO')
def category_edit(request, category_id):
    """Edit existing supply category"""
    category = get_object_or_404(SupplyCategory, id=category_id)

    if request.method == 'POST':
        form = SupplyCategoryForm(request.POST, instance=category)
        if form.is_valid():
            old_data = {
                'name': category.name,
                'description': category.description,
                'color_code': category.color_code,
                'icon_class': category.icon_class,
                'is_active': category.is_active
            }

            updated_category = form.save()

            # Track changes
            new_data = {
                'name': updated_category.name,
                'description': updated_category.description,
                'color_code': updated_category.color_code,
                'icon_class': updated_category.icon_class,
                'is_active': updated_category.is_active
            }

            changes = {k: {'old': old_data[k], 'new': new_data[k]}
                      for k in old_data if old_data[k] != new_data[k]}

            # Log audit event
            log_audit_event(
                user=request.user,
                action_type='UPDATE',
                object_type='SupplyCategory',
                object_id=str(category.id),
                object_repr=str(category),
                changes=changes,
                request=request
            )

            messages.success(request, f'Category "{updated_category.name}" updated successfully.')
            return redirect('supply:category_management')
    else:
        form = SupplyCategoryForm(instance=category)

    context = {
        'form': form,
        'category': category,
        'action': 'Edit',
        'submit_url': reverse('supply:category_edit', args=[category.id])
    }

    return render(request, 'supply/inventory/category_form.html', context)


@login_required
@role_required('GSO')
def category_delete(request, category_id):
    """Delete supply category"""
    category = get_object_or_404(SupplyCategory, id=category_id)

    if request.method == 'POST':
        # Check if category has items
        item_count = category.items.count()

        if item_count > 0:
            messages.error(request, f'Cannot delete category "{category.name}". It contains {item_count} items.')
        else:
            category_name = category.name

            # Log audit event before deletion
            log_audit_event(
                user=request.user,
                action_type='DELETE',
                object_type='SupplyCategory',
                object_id=str(category.id),
                object_repr=str(category),
                changes={'deleted_category': category_name},
                request=request
            )

            category.delete()
            messages.success(request, f'Category "{category_name}" has been deleted.')

    return redirect('supply:category_management')


# Release Management Views

@login_required
@role_required('GSO')
def releases_dashboard(request):
    """Release management dashboard"""
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    release_type_filter = request.GET.get('release_type', '')
    priority_filter = request.GET.get('priority', '')
    search_query = request.GET.get('search', '')
    sort_by = request.GET.get('sort', '-planned_release_date')

    # Base queryset
    releases = Release.objects.select_related('created_by', 'assigned_to', 'approved_by', 'deployed_by')

    # Apply filters
    if status_filter:
        releases = releases.filter(status=status_filter)
    if release_type_filter:
        releases = releases.filter(release_type=release_type_filter)
    if priority_filter:
        releases = releases.filter(priority=priority_filter)
    if search_query:
        releases = releases.filter(
            Q(name__icontains=search_query) |
            Q(version__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(release_id__icontains=search_query)
        )

    # Apply sorting
    releases = releases.order_by(sort_by)

    # Pagination
    paginator = Paginator(releases, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_releases = Release.objects.count()
    active_releases = Release.objects.exclude(status__in=['DEPLOYED', 'CANCELLED', 'FAILED']).count()
    overdue_releases = Release.objects.filter(
        planned_release_date__lt=timezone.now(),
        status__in=['PLANNING', 'DEVELOPMENT', 'TESTING', 'STAGING', 'PENDING_APPROVAL', 'APPROVED']
    ).count()
    pending_approval = Release.objects.filter(status='PENDING_APPROVAL').count()

    # Filter form
    filter_form = ReleaseFilterForm(initial={
        'status': status_filter,
        'release_type': release_type_filter,
        'priority': priority_filter,
        'search': search_query,
    })

    # Chart data
    from django.db.models import Count
    import json

    # Status distribution
    status_data = Release.objects.values('status').annotate(count=Count('status')).order_by('status')
    status_labels = [item['status'].replace('_', ' ').title() for item in status_data]
    status_counts = [item['count'] for item in status_data]

    # Priority distribution
    priority_data = Release.objects.values('priority').annotate(count=Count('priority')).order_by('priority')
    priority_labels = [item['priority'].title() for item in priority_data]
    priority_counts = [item['count'] for item in priority_data]

    # Release type distribution
    type_data = Release.objects.values('release_type').annotate(count=Count('release_type')).order_by('release_type')
    type_labels = [item['release_type'].title() for item in type_data]
    type_counts = [item['count'] for item in type_data]

    # Timeline data (last 6 months)
    from datetime import datetime, timedelta
    from django.db.models.functions import TruncMonth

    six_months_ago = timezone.now() - timedelta(days=180)
    timeline_data = Release.objects.filter(
        created_at__gte=six_months_ago
    ).annotate(
        month=TruncMonth('created_at')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    timeline_labels = [item['month'].strftime('%b %Y') for item in timeline_data]
    timeline_counts = [item['count'] for item in timeline_data]

    context = {
        'page_obj': page_obj,
        'filter_form': filter_form,
        'total_releases': total_releases,
        'active_releases': active_releases,
        'overdue_releases': overdue_releases,
        'pending_approval': pending_approval,
        'status_filter': status_filter,
        'release_type_filter': release_type_filter,
        'priority_filter': priority_filter,
        'search_query': search_query,
        'sort_by': sort_by,
        # Chart data
        'chart_data': json.dumps({
            'status': {'labels': status_labels, 'data': status_counts},
            'priority': {'labels': priority_labels, 'data': priority_counts},
            'type': {'labels': type_labels, 'data': type_counts},
            'timeline': {'labels': timeline_labels, 'data': timeline_counts},
        }),
    }

    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'supply/releases/releases_list.html', context)

    return render(request, 'supply/releases/dashboard.html', context)


@login_required
@role_required('GSO')
def release_create(request):
    """Create a new release"""
    if request.method == 'POST':
        form = ReleaseForm(request.POST)
        if form.is_valid():
            release = form.save(commit=False)
            release.created_by = request.user
            release.save()

            # Log audit event
            log_audit_event(
                user=request.user,
                action_type='CREATE',
                obj=release,
                changes={'created_release': release.name},
                request=request
            )

            messages.success(request, f'Release "{release.name}" has been created successfully.')

            # Return success response for HTMX
            if request.headers.get('HX-Request'):
                return render(request, 'supply/releases/create_success.html', {'release': release})

            return redirect('supply:release_detail', release_id=release.id)
    else:
        form = ReleaseForm()

    context = {'form': form}
    return render(request, 'supply/releases/create.html', context)


@login_required
@role_required('GSO')
def release_detail(request, release_id):
    """Release detail view"""
    release = get_object_or_404(Release, id=release_id)

    context = {
        'release': release,
        'can_approve': release.can_approve(request.user),
        'can_deploy': release.can_deploy(request.user),
    }

    return render(request, 'supply/releases/detail.html', context)


@login_required
@role_required('GSO')
def release_edit(request, release_id):
    """Edit a release"""
    release = get_object_or_404(Release, id=release_id)

    # Prevent editing of deployed releases
    if release.status == 'DEPLOYED':
        messages.error(request, 'Cannot edit a deployed release.')
        return redirect('supply:release_detail', release_id=release.id)

    if request.method == 'POST':
        form = ReleaseForm(request.POST, instance=release)
        if form.is_valid():
            old_data = {
                'name': release.name,
                'version': release.version,
                'status': release.status,
                'priority': release.priority,
            }

            release = form.save()

            # Log audit event
            log_audit_event(
                user=request.user,
                action_type='UPDATE',
                obj=release,
                changes={'old_data': old_data, 'updated_release': release.name},
                request=request
            )

            messages.success(request, f'Release "{release.name}" has been updated successfully.')

            # Return success response for HTMX
            if request.headers.get('HX-Request'):
                return render(request, 'supply/releases/edit_success.html', {'release': release})

            return redirect('supply:release_detail', release_id=release.id)
    else:
        form = ReleaseForm(instance=release)

    context = {'form': form, 'release': release}
    return render(request, 'supply/releases/edit.html', context)


@login_required
@role_required('GSO')
def release_approve(request, release_id):
    """Approve or reject a release"""
    release = get_object_or_404(Release, id=release_id)

    if not release.can_approve(request.user):
        messages.error(request, 'You cannot approve this release.')
        return redirect('supply:release_detail', release_id=release.id)

    if request.method == 'POST':
        form = ReleaseApprovalForm(request.POST)
        if form.is_valid():
            action = form.cleaned_data['action']
            remarks = form.cleaned_data['remarks']

            if action == 'approve':
                if release.approve(request.user, remarks):
                    messages.success(request, f'Release "{release.name}" has been approved.')

                    # Log audit event
                    log_audit_event(
                        user=request.user,
                        action_type='APPROVE',
                        obj=release,
                        changes={'approved_release': release.name, 'remarks': remarks},
                        request=request
                    )
                else:
                    messages.error(request, 'Failed to approve release.')

            elif action == 'reject':
                release.status = 'CANCELLED'
                release.save()
                messages.success(request, f'Release "{release.name}" has been rejected.')

                # Log audit event
                log_audit_event(
                    user=request.user,
                    action_type='REJECT',
                    obj=release,
                    changes={'rejected_release': release.name, 'remarks': remarks},
                    request=request
                )

            elif action == 'request_changes':
                release.status = 'DEVELOPMENT'
                release.save()
                messages.success(request, f'Changes requested for release "{release.name}".')

                # Log audit event
                log_audit_event(
                    user=request.user,
                    action_type='UPDATE',
                    obj=release,
                    changes={'requested_changes': release.name, 'remarks': remarks},
                    request=request
                )

            # Return success response for HTMX
            if request.headers.get('HX-Request'):
                return render(request, 'supply/releases/approval_success.html', {
                    'release': release,
                    'action': action,
                    'remarks': remarks
                })

            return redirect('supply:release_detail', release_id=release.id)
    else:
        form = ReleaseApprovalForm()

    context = {'form': form, 'release': release}
    return render(request, 'supply/releases/approve.html', context)


@login_required
@role_required('GSO')
def release_deploy(request, release_id):
    """Deploy a release"""
    release = get_object_or_404(Release, id=release_id)

    if not release.can_deploy(request.user):
        messages.error(request, 'You cannot deploy this release.')
        return redirect('supply:release_detail', release_id=release.id)

    if request.method == 'POST':
        form = ReleaseDeploymentForm(request.POST)
        if form.is_valid():
            deployment_notes = form.cleaned_data['deployment_notes']

            if release.deploy(request.user, deployment_notes):
                messages.success(request, f'Release "{release.name}" has been deployed successfully.')

                # Log audit event
                log_audit_event(
                    user=request.user,
                    action_type='RELEASE',
                    obj=release,
                    changes={'deployed_release': release.name, 'deployment_notes': deployment_notes},
                    request=request
                )

                # Return success response for HTMX
                if request.headers.get('HX-Request'):
                    return render(request, 'supply/releases/deployment_success.html', {
                        'release': release,
                        'deployment_notes': deployment_notes
                    })

                return redirect('supply:release_detail', release_id=release.id)
            else:
                messages.error(request, 'Failed to deploy release.')
    else:
        form = ReleaseDeploymentForm()

    context = {'form': form, 'release': release}
    return render(request, 'supply/releases/deploy.html', context)


@login_required
@role_required('GSO')
def release_timeline(request):
    """Release timeline view"""
    # Get releases for timeline
    releases = Release.objects.select_related('created_by', 'assigned_to').order_by('planned_release_date')

    # Filter by date range if provided
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        releases = releases.filter(planned_release_date__gte=date_from)
    if date_to:
        releases = releases.filter(planned_release_date__lte=date_to)

    context = {
        'releases': releases,
        'date_from': date_from,
        'date_to': date_to,
    }

    return render(request, 'supply/releases/timeline.html', context)


@login_required
@role_required('GSO')
def release_calendar(request):
    """Release calendar view"""
    # Get releases for calendar
    releases = Release.objects.select_related('created_by', 'assigned_to').exclude(
        status__in=['CANCELLED', 'FAILED']
    )

    # Convert to calendar format
    calendar_events = []
    for release in releases:
        calendar_events.append({
            'id': release.id,
            'title': f"{release.name} v{release.version}",
            'start': release.planned_release_date.isoformat(),
            'url': reverse('supply:release_detail', args=[release.id]),
            'className': f"release-{release.status.lower()}",
            'extendedProps': {
                'status': release.status,
                'priority': release.priority,
                'type': release.release_type,
            }
        })

    context = {
        'calendar_events': calendar_events,
    }

    return render(request, 'supply/releases/calendar.html', context)